import { test, expect, Page } from '@playwright/test';
import path from 'path';

/**
 * 中間結果持久化整合測試
 *
 * 測試 regulation_compliance_executor 的 _save_intermediate_result 方法
 * 驗證中間結果是否正確保存到資料庫和任務配置中
 */

// 測試配置
const TEST_CONFIG = {
  // 測試文件路徑
  testFilePath: 'c:/home/<USER>/repo/backend/uploads/20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf',
  // 後端API基礎URL
  backendUrl: 'http://localhost:8001',
  // 測試超時時間
  uploadTimeout: 60000,
  parseTimeout: 180000, // 3分鐘，因為需要等待法規比對完成
  // 測試數據
  testData: {
    title: '中間結果持久化測試 - 法規比對',
    description: '測試 regulation_compliance_executor 的中間結果持久化功能，包括 clauses_analysis、compliance_check 和 final_report 步驟。'
  }
};

// 輔助函數：等待元素並點擊
async function waitAndClick(page: Page, selector: string, timeout = 10000) {
  await page.waitForSelector(selector, { timeout });
  await page.click(selector);
}

// 輔助函數：等待元素並填寫文字
async function waitAndFill(page: Page, selector: string, text: string, timeout = 10000) {
  await page.waitForSelector(selector, { timeout });
  await page.fill(selector, text);
}

// 輔助函數：檢查API健康狀態
async function checkBackendHealth(page: Page) {
  try {
    const response = await page.request.get(`${TEST_CONFIG.backendUrl}/api/v1/health/`);
    return response.ok();
  } catch (error) {
    console.error('後端健康檢查失敗:', error);
    return false;
  }
}

// 輔助函數：檢查任務的中間結果
async function checkIntermediateResults(page: Page, taskId: string) {
  try {
    // 1. 檢查任務詳情，獲取 config 中的 intermediate_result_ids
    const taskResponse = await page.request.get(`${TEST_CONFIG.backendUrl}/api/v1/task-management/tasks/${taskId}`);
    if (!taskResponse.ok()) {
      console.log('❌ 無法獲取任務詳情');
      return { success: false, details: '無法獲取任務詳情' };
    }

    const taskData = await taskResponse.json();
    console.log('📊 任務狀態:', taskData.status);
    console.log('📊 任務進度:', taskData.progress);
    console.log('📊 當前步驟:', taskData.current_step);

    // 檢查任務配置中的中間結果
    const config = taskData.config || {};
    const intermediateResultIds = config.intermediate_result_ids || {};
    const intermediateResults = config.intermediate_results || {};

    console.log('🔍 中間結果ID映射:', intermediateResultIds);
    console.log('🔍 中間結果數據:', Object.keys(intermediateResults));

    const results = {
      success: true,
      taskStatus: taskData.status,
      progress: taskData.progress,
      currentStep: taskData.current_step,
      intermediateResultIds: intermediateResultIds,
      intermediateResults: intermediateResults,
      details: {}
    };

    // 2. 檢查每個中間結果是否存在於資料庫中
    const expectedSteps = ['clauses_analysis', 'compliance_check', 'final_report'];

    for (const stepName of expectedSteps) {
      const resultId = intermediateResultIds[stepName];
      const resultData = intermediateResults[stepName];

      console.log(`🔍 檢查步驟 ${stepName}:`);
      console.log(`  - 資料庫ID: ${resultId || '無'}`);
      console.log(`  - 配置數據: ${resultData ? '存在' : '無'}`);

      results.details[stepName] = {
        hasResultId: !!resultId,
        hasConfigData: !!resultData,
        resultId: resultId,
        timestamp: resultData?.timestamp
      };

      // 如果有資料庫ID，嘗試查詢分析結果
      if (resultId) {
        try {
          const resultResponse = await page.request.get(
            `${TEST_CONFIG.backendUrl}/api/v1/analysis-results/results/${resultId}?include_content=true`
          );

          if (resultResponse.ok()) {
            const analysisResult = await resultResponse.json();
            console.log(`  ✅ 資料庫中找到結果: ${analysisResult.title}`);
            console.log(`  📄 結果類型: ${analysisResult.result_type}`);
            console.log(`  📊 信心度: ${analysisResult.confidence_score}`);

            results.details[stepName].databaseResult = {
              found: true,
              title: analysisResult.title,
              resultType: analysisResult.result_type,
              confidenceScore: analysisResult.confidence_score,
              hasContent: !!analysisResult.content
            };
          } else {
            console.log(`  ❌ 資料庫中未找到結果: ${resultId}`);
            results.details[stepName].databaseResult = { found: false };
          }
        } catch (error) {
          console.log(`  ⚠️  查詢資料庫結果時出錯: ${error}`);
          results.details[stepName].databaseResult = { found: false, error: error.message };
        }
      }
    }

    return results;

  } catch (error) {
    console.error('檢查中間結果時出錯:', error);
    return { success: false, details: error.message };
  }
}

test.describe('中間結果持久化測試', () => {

  test.beforeEach(async ({ page }) => {
    // 設置測試超時
    test.setTimeout(300000); // 5分鐘

    // 檢查後端服務
    const backendHealthy = await checkBackendHealth(page);
    if (!backendHealthy) {
      throw new Error('後端服務不可用，請確保後端服務運行在 http://localhost:8001');
    }
  });

  test('測試法規比對任務的中間結果持久化', async ({ page }) => {
    console.log('🚀 開始中間結果持久化測試');

    // 步驟1: 從首頁開始
    console.log('📍 步驟1: 訪問首頁');
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 驗證首頁載入
    await expect(page.locator('h1, h2').first()).toContainText(/購案審查|系統|首頁/);
    console.log('✅ 首頁載入成功');

    // 步驟2: 導航到購案分析頁面
    console.log('📍 步驟2: 導航到購案分析頁面');

    const navSelectors = [
      'a[href="/purchase-analysis"]',
      'a:has-text("購案分析")',
      'a:has-text("購案管理")',
      '.nav-link:has-text("購案分析")',
      '.menu-item:has-text("購案分析")',
      'button:has-text("購案分析")',
      '.el-menu-item:has-text("購案分析")'
    ];

    let navClicked = false;
    for (const selector of navSelectors) {
      try {
        const navElement = page.locator(selector);
        if (await navElement.count() > 0 && await navElement.isVisible()) {
          await navElement.click();
          navClicked = true;
          console.log(`✅ 點擊導航成功: ${selector}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }

    if (!navClicked) {
      console.log('⚠️  未找到導航連結，直接訪問購案分析頁面');
      await page.goto('/purchase-analysis');
    }

    await page.waitForLoadState('networkidle');
    await expect(page.locator('h1, h2').first()).toContainText(/購案分析|購案管理|購案審查/);
    console.log('✅ 購案分析頁面載入成功');

    // 步驟3: 選擇單檔案上傳模式
    console.log('📍 步驟3: 選擇單檔案上傳模式');
    await page.waitForTimeout(2000);

    const singleModeRadio = page.locator('.el-radio-button[label="single"]');
    if (await singleModeRadio.count() > 0) {
      await singleModeRadio.click();
      console.log('✅ 已選擇單檔案上傳模式');
    }

    // 步驟4: 上傳PDF文件
    console.log('📍 步驟4: 上傳PDF文件');
    await page.waitForTimeout(2000);

    const fileInputSelectors = [
      'input[type="file"]',
      '.el-upload input[type="file"]',
      '.file-upload input[type="file"]',
      '.upload-dragger input[type="file"]'
    ];

    let fileInput = null;
    for (const selector of fileInputSelectors) {
      const elements = page.locator(selector);
      if (await elements.count() > 0) {
        fileInput = elements.first();
        console.log(`✅ 找到文件輸入框: ${selector}`);
        break;
      }
    }

    if (!fileInput) {
      throw new Error('無法找到文件輸入框');
    }

    await fileInput.setInputFiles(TEST_CONFIG.testFilePath);
    console.log(`✅ 已選擇文件: ${TEST_CONFIG.testFilePath}`);

    // 步驟5: 填寫購案信息
    console.log('📍 步驟5: 填寫購案信息');
    await page.waitForSelector('input[placeholder*="標題"], input[placeholder*="title"]', { timeout: 15000 });

    // 填寫標題
    const titleSelectors = [
      'input[placeholder*="標題"]',
      'input[placeholder*="title"]',
      'input[name="title"]',
      '.el-input__inner[placeholder*="標題"]'
    ];

    for (const selector of titleSelectors) {
      try {
        const titleInput = page.locator(selector);
        if (await titleInput.count() > 0) {
          await titleInput.fill(TEST_CONFIG.testData.title);
          console.log('✅ 已填寫購案標題');
          break;
        }
      } catch (error) {
        continue;
      }
    }

    // 填寫描述
    const descriptionSelectors = [
      'textarea[placeholder*="描述"]',
      'textarea[placeholder*="description"]',
      'textarea[name="description"]',
      '.el-textarea__inner'
    ];

    for (const selector of descriptionSelectors) {
      try {
        const descInput = page.locator(selector);
        if (await descInput.count() > 0) {
          await descInput.fill(TEST_CONFIG.testData.description);
          console.log('✅ 已填寫購案描述');
          break;
        }
      } catch (error) {
        continue;
      }
    }

    // 步驟6: 確保自動開始解析選項已勾選
    console.log('📍 步驟6: 設置自動開始解析');
    const autoStartSelectors = [
      'input[type="checkbox"]',
      '.el-checkbox__input input',
      'input[name="autoStart"]'
    ];

    for (const selector of autoStartSelectors) {
      try {
        const checkboxes = page.locator(selector);
        const count = await checkboxes.count();

        for (let i = 0; i < count; i++) {
          const checkbox = checkboxes.nth(i);
          const isVisible = await checkbox.isVisible();
          if (isVisible && !(await checkbox.isChecked())) {
            await checkbox.click();
            console.log('✅ 已勾選自動開始解析');
            break;
          }
        }
      } catch (error) {
        continue;
      }
    }

    // 步驟7: 提交表單開始上傳
    console.log('📍 步驟7: 提交表單開始上傳');
    const submitSelectors = [
      'button:has-text("開始上傳")',
      'button:has-text("提交")',
      'button:has-text("上傳")',
      '.el-button--primary',
      'button[type="submit"]'
    ];

    let submitted = false;
    for (const selector of submitSelectors) {
      try {
        const submitBtn = page.locator(selector);
        if (await submitBtn.count() > 0 && await submitBtn.isVisible()) {
          await submitBtn.click();
          submitted = true;
          console.log('✅ 已提交上傳表單');
          break;
        }
      } catch (error) {
        continue;
      }
    }

    if (!submitted) {
      throw new Error('無法找到提交按鈕');
    }

    // 步驟8: 等待跳轉到結果頁面
    console.log('📍 步驟8: 等待跳轉到結果頁面');
    await page.waitForURL(/\/results/, { timeout: TEST_CONFIG.uploadTimeout });
    console.log('✅ 已跳轉到結果頁面');

    // 從URL中提取購案ID和任務ID
    const currentUrl = page.url();
    console.log(`🔍 當前URL: ${currentUrl}`);

    // 提取購案ID
    const purchaseIdMatch = currentUrl.match(/[?&#]purchase_id=([^&]+)/);
    let purchaseId = null;
    if (purchaseIdMatch) {
      purchaseId = purchaseIdMatch[1];
      console.log(`📋 購案ID: ${purchaseId}`);
    }

    // 嘗試多種方式來獲取任務ID
    let taskId = null;

    // 方式1: 從URL中直接提取任務ID
    let taskIdMatch = currentUrl.match(/[?&#]taskId=([^&]+)/);
    if (taskIdMatch) {
      taskId = taskIdMatch[1];
      console.log(`📋 從URL獲取任務ID: ${taskId}`);
    }

    // 方式2: 如果有購案ID，查詢該購案的最新任務
    if (!taskId && purchaseId) {
      console.log('🔍 使用購案ID查詢相關任務...');
      try {
        const tasksResponse = await page.request.get(
          `${TEST_CONFIG.backendUrl}/api/v1/task-management/tasks?purchase_id=${purchaseId}&limit=10&sort_by=created_time&sort_order=desc`
        );
        if (tasksResponse.ok()) {
          const tasksData = await tasksResponse.json();

          // 處理不同的API響應格式
          let tasks = [];
          if (Array.isArray(tasksData)) {
            tasks = tasksData;
          } else if (tasksData.tasks && Array.isArray(tasksData.tasks)) {
            tasks = tasksData.tasks;
          } else if (tasksData.data && Array.isArray(tasksData.data)) {
            tasks = tasksData.data;
          }

          console.log(`📊 找到 ${tasks.length} 個相關任務`);

          if (tasks.length > 0) {
            // 優先選擇法規比對任務
            const regulationTask = tasks.find(task =>
              task.task_type === 'regulation_compliance' ||
              task.task_name?.includes('法規比對') ||
              task.task_name?.includes('regulation')
            );

            if (regulationTask) {
              taskId = regulationTask.task_id;
              console.log(`📋 找到法規比對任務: ${taskId}`);
            } else {
              // 如果沒有法規比對任務，使用最新的任務
              taskId = tasks[0].task_id;
              console.log(`📋 使用最新任務: ${taskId} (類型: ${tasks[0].task_type})`);
            }
          }
        } else {
          console.log('❌ 任務查詢API請求失敗');
        }
      } catch (error) {
        console.log('❌ 查詢任務時出錯:', error);
      }
    }

    // 方式3: 如果還是沒有任務ID，查詢所有最新任務
    if (!taskId) {
      console.log('🔍 查詢系統中的最新任務...');
      try {
        const tasksResponse = await page.request.get(
          `${TEST_CONFIG.backendUrl}/api/v1/task-management/tasks?limit=10&sort_by=created_time&sort_order=desc`
        );
        if (tasksResponse.ok()) {
          const tasksData = await tasksResponse.json();

          // 處理不同的API響應格式
          let tasks = [];
          if (Array.isArray(tasksData)) {
            tasks = tasksData;
          } else if (tasksData.tasks && Array.isArray(tasksData.tasks)) {
            tasks = tasksData.tasks;
          } else if (tasksData.data && Array.isArray(tasksData.data)) {
            tasks = tasksData.data;
          }

          console.log(`📊 系統中共有 ${tasks.length} 個任務`);

          if (tasks.length > 0) {
            // 優先選擇法規比對任務
            const regulationTask = tasks.find(task =>
              task.task_type === 'regulation_compliance'
            );

            if (regulationTask) {
              taskId = regulationTask.task_id;
              console.log(`📋 找到法規比對任務: ${taskId}`);
            } else {
              taskId = tasks[0].task_id;
              console.log(`📋 使用最新任務: ${taskId} (類型: ${tasks[0].task_type})`);
            }
          }
        }
      } catch (error) {
        console.log('❌ 查詢最新任務時出錯:', error);
      }
    }

    if (!taskId) {
      throw new Error('無法獲取任務ID，請檢查API連接或確保有可用的任務');
    }

    console.log(`📋 最終使用任務ID: ${taskId}`);

    // 步驟9: 啟動新的法規比對任務
    console.log('📍 步驟9: 啟動新的法規比對任務');

    // 首先檢查是否有現有的法規比對任務正在運行
    let hasRunningRegulationTask = false;
    if (purchaseId) {
      try {
        const tasksResponse = await page.request.get(
          `${TEST_CONFIG.backendUrl}/api/v1/task-management/tasks?purchase_id=${purchaseId}&task_type=regulation_compliance&status=running`
        );
        if (tasksResponse.ok()) {
          const tasksData = await tasksResponse.json();
          let tasks = Array.isArray(tasksData) ? tasksData : (tasksData.tasks || tasksData.data || []);
          hasRunningRegulationTask = tasks.length > 0;
          if (hasRunningRegulationTask) {
            taskId = tasks[0].task_id;
            console.log(`📋 發現正在運行的法規比對任務: ${taskId}`);
          }
        }
      } catch (error) {
        console.log('⚠️  檢查運行中任務時出錯:', error);
      }
    }

    // 如果沒有運行中的法規比對任務，創建一個新的
    if (!hasRunningRegulationTask) {
      console.log('🚀 創建新的法規比對任務...');
      try {
        // 使用解析API啟動法規比對任務
        const startTaskResponse = await page.request.post(`${TEST_CONFIG.backendUrl}/api/v1/parse/start`, {
          data: {
            purchase_id: purchaseId,
            task_types: ['regulation_compliance'],
            auto_start: true
          }
        });

        if (startTaskResponse.ok()) {
          const startResult = await startTaskResponse.json();
          console.log('✅ 成功啟動法規比對任務');
          console.log('📋 任務詳情:', startResult);

          // 從響應中提取任務ID
          if (startResult.tasks && startResult.tasks.length > 0) {
            const regulationTask = startResult.tasks.find(task => task.task_type === 'regulation_compliance');
            if (regulationTask) {
              taskId = regulationTask.task_id;
              console.log(`📋 新建法規比對任務ID: ${taskId}`);
            }
          } else if (startResult.task_id) {
            taskId = startResult.task_id;
            console.log(`📋 新建任務ID: ${taskId}`);
          }
        } else {
          console.log('❌ 啟動法規比對任務失敗');
          console.log('響應狀態:', startTaskResponse.status());
        }
      } catch (error) {
        console.log('❌ 創建法規比對任務時出錯:', error);
      }
    }

    // 如果還是沒有任務ID，嘗試點擊頁面上的開始解析按鈕
    if (!taskId) {
      console.log('🔍 嘗試點擊頁面上的開始解析按鈕...');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      const startParseSelectors = [
        'button:has-text("開始解析")',
        'button:has-text("開始")',
        '.el-button:has-text("開始解析")',
        '.parse-button',
        '.start-parse-btn'
      ];

      let parseStarted = false;
      for (const selector of startParseSelectors) {
        try {
          const parseButton = page.locator(selector);
          if (await parseButton.count() > 0 && await parseButton.isVisible()) {
            await parseButton.click();
            parseStarted = true;
            console.log(`✅ 點擊開始解析按鈕成功: ${selector}`);
            break;
          }
        } catch (error) {
          continue;
        }
      }

      if (!parseStarted) {
        console.log('⚠️  未找到開始解析按鈕');
      }

      // 等待任務創建後再次查詢
      await page.waitForTimeout(5000);
      if (purchaseId) {
        try {
          const tasksResponse = await page.request.get(
            `${TEST_CONFIG.backendUrl}/api/v1/task-management/tasks?purchase_id=${purchaseId}&limit=5&sort_by=created_time&sort_order=desc`
          );
          if (tasksResponse.ok()) {
            const tasksData = await tasksResponse.json();
            let tasks = Array.isArray(tasksData) ? tasksData : (tasksData.tasks || tasksData.data || []);
            const regulationTask = tasks.find(task => task.task_type === 'regulation_compliance');
            if (regulationTask) {
              taskId = regulationTask.task_id;
              console.log(`📋 找到新創建的法規比對任務: ${taskId}`);
            }
          }
        } catch (error) {
          console.log('⚠️  查詢新任務時出錯:', error);
        }
      }
    }

    if (!taskId) {
      throw new Error('無法創建或找到法規比對任務');
    }

    // 步驟10: 監控任務執行並檢查中間結果持久化
    console.log('📍 步驟10: 監控任務執行並檢查中間結果持久化');

    const maxWaitTime = TEST_CONFIG.parseTimeout;
    const startTime = Date.now();
    let lastProgress = -1;
    let intermediateResultsChecked = {
      clauses_analysis: false,
      compliance_check: false,
      final_report: false
    };

    while (Date.now() - startTime < maxWaitTime) {
      try {
        // 檢查任務狀態和中間結果
        const checkResult = await checkIntermediateResults(page, taskId);

        if (checkResult.success) {
          const { taskStatus, progress, currentStep, details } = checkResult;

          // 只在進度變化時輸出
          if (progress !== lastProgress) {
            console.log(`📊 任務進度: ${progress}% - ${currentStep || taskStatus}`);
            lastProgress = progress;
          }

          // 檢查各個中間結果步驟
          for (const stepName of Object.keys(intermediateResultsChecked)) {
            if (!intermediateResultsChecked[stepName] && details[stepName]) {
              const stepDetail = details[stepName];

              if (stepDetail.hasResultId || stepDetail.hasConfigData) {
                console.log(`🎯 發現 ${stepName} 中間結果:`);
                console.log(`  - 資料庫ID: ${stepDetail.resultId || '無'}`);
                console.log(`  - 配置數據: ${stepDetail.hasConfigData ? '存在' : '無'}`);
                console.log(`  - 時間戳: ${stepDetail.timestamp || '無'}`);

                if (stepDetail.databaseResult?.found) {
                  console.log(`  - 資料庫驗證: ✅ 成功`);
                  console.log(`  - 結果標題: ${stepDetail.databaseResult.title}`);
                  console.log(`  - 結果類型: ${stepDetail.databaseResult.resultType}`);
                  console.log(`  - 信心度: ${stepDetail.databaseResult.confidenceScore}`);
                } else if (stepDetail.hasResultId) {
                  console.log(`  - 資料庫驗證: ❌ 失敗`);
                }

                intermediateResultsChecked[stepName] = true;
              }
            }
          }

          // 如果任務完成，進行最終檢查
          if (taskStatus === 'completed' || taskStatus === 'failed') {
            console.log(`🏁 任務已完成，狀態: ${taskStatus}`);
            break;
          }

          // 如果所有中間結果都已檢查到，可以提前結束
          if (Object.values(intermediateResultsChecked).every(checked => checked)) {
            console.log('🎉 所有中間結果都已成功檢測到！');
            // 繼續等待任務完成以進行最終驗證
          }

        } else {
          console.log('⚠️  檢查中間結果時出錯:', checkResult.details);
        }

        // 等待5秒後再次檢查
        await page.waitForTimeout(5000);

      } catch (error) {
        console.log('⚠️  監控過程中出錯:', error);
        await page.waitForTimeout(5000);
      }
    }

    // 步驟11: 最終驗證中間結果持久化
    console.log('📍 步驟11: 最終驗證中間結果持久化');

    const finalCheck = await checkIntermediateResults(page, taskId);

    if (finalCheck.success) {
      console.log('📊 最終檢查結果:');
      console.log(`  - 任務狀態: ${finalCheck.taskStatus}`);
      console.log(`  - 任務進度: ${finalCheck.progress}%`);

      // 驗證每個中間結果
      const expectedSteps = ['clauses_analysis', 'compliance_check', 'final_report'];
      let allStepsValid = true;

      for (const stepName of expectedSteps) {
        const stepDetail = finalCheck.details[stepName];
        console.log(`\n🔍 ${stepName} 驗證:`);

        if (stepDetail) {
          console.log(`  - 資料庫ID: ${stepDetail.hasResultId ? '✅' : '❌'} ${stepDetail.resultId || ''}`);
          console.log(`  - 配置數據: ${stepDetail.hasConfigData ? '✅' : '❌'}`);
          console.log(`  - 資料庫查詢: ${stepDetail.databaseResult?.found ? '✅' : '❌'}`);

          // 檢查是否至少有一種持久化方式成功
          const isValid = stepDetail.hasResultId || stepDetail.hasConfigData;
          if (!isValid) {
            allStepsValid = false;
            console.log(`  ❌ ${stepName} 持久化失敗`);
          } else {
            console.log(`  ✅ ${stepName} 持久化成功`);
          }
        } else {
          console.log(`  ❌ 未找到 ${stepName} 的任何數據`);
          allStepsValid = false;
        }
      }

      // 最終斷言
      if (allStepsValid) {
        console.log('\n🎉 中間結果持久化測試完全成功！');
        console.log('✅ 所有中間步驟都已正確持久化');

        // 驗證至少有一個中間結果被保存
        const hasAnyIntermediateResult = expectedSteps.some(step => {
          const detail = finalCheck.details[step];
          return detail && (detail.hasResultId || detail.hasConfigData);
        });

        expect(hasAnyIntermediateResult).toBeTruthy();

      } else {
        console.log('\n❌ 中間結果持久化測試失敗');
        console.log('部分中間步驟未正確持久化');

        // 截圖以便調試
        await page.screenshot({
          path: 'test-results/intermediate-persistence-debug.png',
          fullPage: true
        });

        throw new Error('中間結果持久化驗證失敗');
      }

    } else {
      console.log('❌ 最終檢查失敗:', finalCheck.details);
      throw new Error('無法進行最終的中間結果驗證');
    }

    console.log('✅ 中間結果持久化整合測試完成');
  });

  test('驗證中間結果API端點', async ({ page }) => {
    console.log('🔍 測試中間結果API端點');

    // 使用一個已知的任務ID進行測試
    const knownTaskId = 'aa1c829a-e3d5-4b96-b0d4-075bec65a10f';

    // 1. 測試任務管理API
    console.log('📊 測試任務管理API...');
    const taskResponse = await page.request.get(`${TEST_CONFIG.backendUrl}/api/v1/task-management/tasks/${knownTaskId}`);

    if (taskResponse.ok()) {
      const taskData = await taskResponse.json();
      console.log('✅ 任務管理API可用');
      console.log('📋 任務狀態:', taskData.status);
      console.log('📋 任務配置:', taskData.config ? '存在' : '無');

      // 檢查中間結果配置
      if (taskData.config) {
        const intermediateResultIds = taskData.config.intermediate_result_ids || {};
        const intermediateResults = taskData.config.intermediate_results || {};

        console.log('🔍 中間結果ID數量:', Object.keys(intermediateResultIds).length);
        console.log('🔍 中間結果數據數量:', Object.keys(intermediateResults).length);
      }
    } else {
      console.log('❌ 任務管理API不可用');
    }

    // 2. 測試分析結果API
    console.log('📄 測試分析結果API...');
    const resultsResponse = await page.request.get(
      `${TEST_CONFIG.backendUrl}/api/v1/analysis-results/results?result_type=INTERMEDIATE&limit=5`
    );

    if (resultsResponse.ok()) {
      const resultsData = await resultsResponse.json();
      console.log('✅ 分析結果API可用');
      console.log('📄 中間結果數量:', resultsData.results?.length || 0);

      if (resultsData.results && resultsData.results.length > 0) {
        const firstResult = resultsData.results[0];
        console.log('📄 第一個中間結果:');
        console.log('  - ID:', firstResult.result_id);
        console.log('  - 標題:', firstResult.title);
        console.log('  - 類型:', firstResult.result_type);
        console.log('  - 狀態:', firstResult.status);
      }
    } else {
      console.log('❌ 分析結果API不可用');
    }

    // 3. 測試健康檢查
    console.log('🏥 測試後端健康檢查...');
    const healthResponse = await page.request.get(`${TEST_CONFIG.backendUrl}/api/v1/health/`);
    expect(healthResponse.ok()).toBeTruthy();

    const healthData = await healthResponse.json();
    expect(healthData).toHaveProperty('status');
    console.log('✅ 後端健康檢查通過');

    console.log('✅ API端點測試完成');
  });
});
